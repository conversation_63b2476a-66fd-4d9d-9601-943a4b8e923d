"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.UnexpectedStateError = void 0;

var _es6Error = _interopRequireDefault(require("es6-error"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/* eslint-disable fp/no-class, fp/no-this */
class UnexpectedStateError extends _es6Error.default {
  constructor(message, code = 'UNEXPECTED_STATE_ERROR') {
    super(message);
    this.code = code;
  }

}

exports.UnexpectedStateError = UnexpectedStateError;
//# sourceMappingURL=errors.js.map