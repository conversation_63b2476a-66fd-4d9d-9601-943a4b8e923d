{"version": 3, "sources": ["../../src/factories/createLogger.js"], "names": ["globalThis", "domain", "environmentIsNode", "require", "getParentDomainContext", "parentRoarrContexts", "currentDomain", "process", "parentDomain", "roarr", "context", "push", "domainContext", "parentRoarrContext", "getFirstParentDomainContext", "createLogger", "onMessage", "parentContext", "log", "a", "b", "c", "d", "e", "f", "g", "h", "i", "k", "time", "Date", "now", "sequence", "ROARR", "message", "args", "values", "Object", "keys", "map", "key", "hasOnlyOneParameterValued", "reduce", "accumulator", "value", "TypeError", "JSON", "parse", "version", "child", "getContext", "adopt", "routine", "<PERSON><PERSON><PERSON><PERSON>", "create", "run", "logLevel", "logLevels"], "mappings": ";;;;;;;AAEA;;AACA;;AACA;;AACA;;AAGA;;;;;;;;;;;;AAUA,MAAMA,UAAU,GAAG,0BAAnB;AAEA,IAAIC,MAAJ;;AAEA,IAAIC,mBAAJ,EAAuB;AACrB;AACAD,EAAAA,MAAM,GAAGE,OAAO,CAAC,QAAD,CAAhB;AACD;;AAED,MAAMC,sBAAsB,GAAG,MAAM;AACnC,MAAI,CAACH,MAAL,EAAa;AACX,WAAO,EAAP;AACD;;AAED,QAAMI,mBAAmB,GAAG,EAA5B;AAEA,MAAIC,aAAa,GAAGC,OAAO,CAACN,MAA5B,CAPmC,CASnC;;AACA,MAAI,CAACK,aAAD,IAAkB,CAACA,aAAa,CAACE,YAArC,EAAmD;AACjD,WAAO,EAAP;AACD;;AAED,SAAOF,aAAa,IAAIA,aAAa,CAACE,YAAtC,EAAoD;AAClDF,IAAAA,aAAa,GAAGA,aAAa,CAACE,YAA9B;;AAEA,QAAIF,aAAa,CAACG,KAAd,IAAuBH,aAAa,CAACG,KAAd,CAAoBC,OAA/C,EAAwD;AACtDL,MAAAA,mBAAmB,CAACM,IAApB,CAAyBL,aAAa,CAACG,KAAd,CAAoBC,OAA7C;AACD;AACF;;AAED,MAAIE,aAAa,GAAG,EAApB;;AAEA,OAAK,MAAMC,kBAAX,IAAiCR,mBAAjC,EAAsD;AACpDO,IAAAA,aAAa,mCACRA,aADQ,GAERC,kBAFQ,CAAb;AAID;;AAED,SAAOD,aAAP;AACD,CAhCD;;AAkCA,MAAME,2BAA2B,GAAG,MAAM;AACxC,MAAI,CAACb,MAAL,EAAa;AACX,WAAO,EAAP;AACD;;AAED,MAAIK,aAAa,GAAGC,OAAO,CAACN,MAA5B,CALwC,CAOxC;;AACA,MAAIK,aAAa,IAAIA,aAAa,CAACG,KAA/B,IAAwCH,aAAa,CAACG,KAAd,CAAoBC,OAAhE,EAAyE;AACvE,WAAOJ,aAAa,CAACG,KAAd,CAAoBC,OAA3B;AACD,GAVuC,CAYxC;;;AACA,MAAI,CAACJ,aAAD,IAAkB,CAACA,aAAa,CAACE,YAArC,EAAmD;AACjD,WAAO,EAAP;AACD;;AAED,SAAOF,aAAa,IAAIA,aAAa,CAACE,YAAtC,EAAoD;AAClDF,IAAAA,aAAa,GAAGA,aAAa,CAACE,YAA9B;;AAEA,QAAIF,aAAa,CAACG,KAAd,IAAuBH,aAAa,CAACG,KAAd,CAAoBC,OAA/C,EAAwD;AACtD,aAAOJ,aAAa,CAACG,KAAd,CAAoBC,OAA3B;AACD;AACF;;AAED,SAAO,EAAP;AACD,CA1BD;;AA4BA,MAAMK,YAAY,GAAG,CAACC,SAAD,EAAqCC,aAArC,KAAwF;AAC3G;AACA,QAAMC,GAAG,GAAG,CAACC,CAAD,EAAIC,CAAJ,EAAOC,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBC,CAAnB,EAAsBC,CAAtB,EAAyBC,CAAzB,EAA4BC,CAA5B,KAAkC;AAC5C,UAAMC,IAAI,GAAGC,IAAI,CAACC,GAAL,EAAb;AACA,UAAMC,QAAQ,GAAGhC,UAAU,CAACiC,KAAX,CAAiBD,QAAjB,EAAjB;AAEA,QAAItB,OAAJ;AACA,QAAIwB,OAAJ;;AAEA,QAAI,OAAOf,CAAP,KAAa,QAAjB,EAA2B;AACzBT,MAAAA,OAAO,mCACFI,2BAA2B,EADzB,GAEFG,aAAa,IAAI,EAFf,CAAP,CADyB,CAKzB;;AACA,YAAUkB,IAAV,gBAAkB;AAAChB,QAAAA,CAAD;AAAIC,QAAAA,CAAJ;AAAOC,QAAAA,CAAP;AAAUC,QAAAA,CAAV;AAAaC,QAAAA,CAAb;AAAgBC,QAAAA,CAAhB;AAAmBC,QAAAA,CAAnB;AAAsBC,QAAAA,CAAtB;AAAyBC,QAAAA,CAAzB;AAA4BC,QAAAA;AAA5B,OAAlB;;AACA,YAAMQ,MAAM,GAAGC,MAAM,CAACC,IAAP,CAAYH,IAAZ,EAAkBI,GAAlB,CAAuBC,GAAD,IAAS;AAC5C,eAAOL,IAAI,CAACK,GAAD,CAAX;AACD,OAFc,CAAf,CAPyB,CAUzB;;AACA,YAAMC,yBAAyB,GAAG,MAAML,MAAM,CAACM,MAAP,CAAc,CAACC,WAAD,EAAcC,KAAd,KAAwB;AAC5E;AACA,eAAOD,WAAW,IAAI,OAAOC,KAAP,KAAiB,WAAjB,GAA+B,CAA/B,GAAmC,CAAzD;AACD,OAHuC,EAGrC,CAHqC,CAAxC;AAIAV,MAAAA,OAAO,GAAGO,yBAAyB,GAAG,wBAAQ,IAAR,EAActB,CAAd,CAAH,GAAsB,wBAAQA,CAAR,EAAWC,CAAX,EAAcC,CAAd,EAAiBC,CAAjB,EAAoBC,CAApB,EAAuBC,CAAvB,EAA0BC,CAA1B,EAA6BC,CAA7B,EAAgCC,CAAhC,EAAmCC,CAAnC,CAAzD;AACD,KAhBD,MAgBO;AACL,UAAI,OAAOR,CAAP,KAAa,QAAjB,EAA2B;AACzB,cAAM,IAAIyB,SAAJ,CAAc,2BAAd,CAAN;AACD;;AAEDnC,MAAAA,OAAO,GAAGoC,IAAI,CAACC,KAAL,CAAW,8EAChBjC,2BAA2B,EADX,GAEhBG,aAAa,IAAI,EAFD,GAGhBE,CAHgB,EAAX,CAAV;AAMAe,MAAAA,OAAO,GAAG,wBAAQd,CAAR,EAAWC,CAAX,EAAcC,CAAd,EAAiBC,CAAjB,EAAoBC,CAApB,EAAuBC,CAAvB,EAA0BC,CAA1B,EAA6BC,CAA7B,EAAgCC,CAAhC,CAAV;AACD;;AAEDZ,IAAAA,SAAS,CAAC;AACRN,MAAAA,OADQ;AAERwB,MAAAA,OAFQ;AAGRF,MAAAA,QAHQ;AAIRH,MAAAA,IAJQ;AAKRmB,MAAAA,OAAO,EAAE;AALD,KAAD,CAAT;AAOD,GA5CD;;AA8CA9B,EAAAA,GAAG,CAAC+B,KAAJ,GAAavC,OAAD,IAA4E;AACtF,QAAI,OAAOA,OAAP,KAAmB,UAAvB,EAAmC;AACjC,aAAOK,YAAY,CAAEmB,OAAD,IAAa;AAC/B,YAAI,OAAOxB,OAAP,KAAmB,UAAvB,EAAmC;AACjC,gBAAM,IAAImC,SAAJ,CAAc,mBAAd,CAAN;AACD;;AACD7B,QAAAA,SAAS,CAACN,OAAO,CAACwB,OAAD,CAAR,CAAT;AACD,OALkB,EAKhBjB,aALgB,CAAnB;AAMD;;AAED,WAAOF,YAAY,CAACC,SAAD,gDACdF,2BAA2B,EADb,GAEdG,aAFc,GAGdP,OAHc,EAAnB;AAKD,GAfD;;AAiBAQ,EAAAA,GAAG,CAACgC,UAAJ,GAAiB,MAA0B;AACzC,2CACKpC,2BAA2B,EADhC,GAEKG,aAAa,IAAI,EAFtB;AAID,GALD;;AAOAC,EAAAA,GAAG,CAACiC,KAAJ,GAAY,OAAOC,OAAP,EAAgB1C,OAAhB,KAA4B;AACtC,QAAI,CAACT,MAAL,EAAa;AACX,aAAOmD,OAAO,EAAd;AACD;;AAED,UAAMC,aAAa,GAAGpD,MAAM,CAACqD,MAAP,EAAtB;AAEA,WAAOD,aAAa,CACjBE,GADI,CACA,MAAM;AACT;AACAF,MAAAA,aAAa,CAAC5C,KAAd,GAAsB;AACpBC,QAAAA,OAAO,kCACFN,sBAAsB,EADpB,GAEFM,OAFE;AADa,OAAtB;AAOA,aAAO0C,OAAO,EAAd;AACD,KAXI,CAAP;AAYD,GAnBD;;AAqBA,OAAK,MAAMI,QAAX,IAAuBnB,MAAM,CAACC,IAAP,CAAYmB,oBAAZ,CAAvB,EAA+C;AAC7C;AACAvC,IAAAA,GAAG,CAACsC,QAAD,CAAH,GAAgB,CAACrC,CAAD,EAAIC,CAAJ,EAAOC,CAAP,EAAUC,CAAV,EAAaC,CAAb,EAAgBC,CAAhB,EAAmBC,CAAnB,EAAsBC,CAAtB,EAAyBC,CAAzB,EAA4BC,CAA5B,KAAkC;AAChD,aAAOV,GAAG,CAAC+B,KAAJ,CAAU;AACfO,QAAAA,QAAQ,EAAEC,qBAAUD,QAAV;AADK,OAAV,EAEJrC,CAFI,EAEDC,CAFC,EAEEC,CAFF,EAEKC,CAFL,EAEQC,CAFR,EAEWC,CAFX,EAEcC,CAFd,EAEiBC,CAFjB,EAEoBC,CAFpB,EAEuBC,CAFvB,CAAP;AAGD,KAJD;AAKD,GApG0G,CAsG3G;AACA;;;AACA,SAAOV,GAAP;AACD,CAzGD;;eA2GeH,Y", "sourcesContent": ["// @flow\n\nimport environmentIsNode from 'detect-node';\nimport createGlobalThis from 'globalthis';\nimport stringify from 'json-stringify-safe';\nimport {\n  sprintf,\n} from 'sprintf-js';\nimport {\n  logLevels,\n} from '../constants';\nimport type {\n  LoggerType,\n  MessageContextType,\n  MessageEventHandlerType,\n  TranslateMessageFunctionType,\n} from '../types';\n\nconst globalThis = createGlobalThis();\n\nlet domain;\n\nif (environmentIsNode) {\n  // eslint-disable-next-line global-require\n  domain = require('domain');\n}\n\nconst getParentDomainContext = () => {\n  if (!domain) {\n    return {};\n  }\n\n  const parentRoarrContexts = [];\n\n  let currentDomain = process.domain;\n\n  // $FlowFixMe\n  if (!currentDomain || !currentDomain.parentDomain) {\n    return {};\n  }\n\n  while (currentDomain && currentDomain.parentDomain) {\n    currentDomain = currentDomain.parentDomain;\n\n    if (currentDomain.roarr && currentDomain.roarr.context) {\n      parentRoarrContexts.push(currentDomain.roarr.context);\n    }\n  }\n\n  let domainContext = {};\n\n  for (const parentRoarrContext of parentRoarrContexts) {\n    domainContext = {\n      ...domainContext,\n      ...parentRoarrContext,\n    };\n  }\n\n  return domainContext;\n};\n\nconst getFirstParentDomainContext = () => {\n  if (!domain) {\n    return {};\n  }\n\n  let currentDomain = process.domain;\n\n  // $FlowFixMe\n  if (currentDomain && currentDomain.roarr && currentDomain.roarr.context) {\n    return currentDomain.roarr.context;\n  }\n\n  // $FlowFixMe\n  if (!currentDomain || !currentDomain.parentDomain) {\n    return {};\n  }\n\n  while (currentDomain && currentDomain.parentDomain) {\n    currentDomain = currentDomain.parentDomain;\n\n    if (currentDomain.roarr && currentDomain.roarr.context) {\n      return currentDomain.roarr.context;\n    }\n  }\n\n  return {};\n};\n\nconst createLogger = (onMessage: MessageEventHandlerType, parentContext?: MessageContextType): LoggerType => {\n  // eslint-disable-next-line id-length, unicorn/prevent-abbreviations\n  const log = (a, b, c, d, e, f, g, h, i, k) => {\n    const time = Date.now();\n    const sequence = globalThis.ROARR.sequence++;\n\n    let context;\n    let message;\n\n    if (typeof a === 'string') {\n      context = {\n        ...getFirstParentDomainContext(),\n        ...parentContext || {},\n      };\n      // eslint-disable-next-line id-length, object-property-newline\n      const {...args} = {a, b, c, d, e, f, g, h, i, k};\n      const values = Object.keys(args).map((key) => {\n        return args[key];\n      });\n      // eslint-disable-next-line unicorn/no-reduce\n      const hasOnlyOneParameterValued = 1 === values.reduce((accumulator, value) => {\n        // eslint-disable-next-line no-return-assign, no-param-reassign\n        return accumulator += typeof value === 'undefined' ? 0 : 1;\n      }, 0);\n      message = hasOnlyOneParameterValued ? sprintf('%s', a) : sprintf(a, b, c, d, e, f, g, h, i, k);\n    } else {\n      if (typeof b !== 'string') {\n        throw new TypeError('Message must be a string.');\n      }\n\n      context = JSON.parse(stringify({\n        ...getFirstParentDomainContext(),\n        ...parentContext || {},\n        ...a,\n      }));\n\n      message = sprintf(b, c, d, e, f, g, h, i, k);\n    }\n\n    onMessage({\n      context,\n      message,\n      sequence,\n      time,\n      version: '1.0.0',\n    });\n  };\n\n  log.child = (context: TranslateMessageFunctionType | MessageContextType): LoggerType => {\n    if (typeof context === 'function') {\n      return createLogger((message) => {\n        if (typeof context !== 'function') {\n          throw new TypeError('Unexpected state.');\n        }\n        onMessage(context(message));\n      }, parentContext);\n    }\n\n    return createLogger(onMessage, {\n      ...getFirstParentDomainContext(),\n      ...parentContext,\n      ...context,\n    });\n  };\n\n  log.getContext = (): MessageContextType => {\n    return {\n      ...getFirstParentDomainContext(),\n      ...parentContext || {},\n    };\n  };\n\n  log.adopt = async (routine, context) => {\n    if (!domain) {\n      return routine();\n    }\n\n    const adoptedDomain = domain.create();\n\n    return adoptedDomain\n      .run(() => {\n        // $FlowFixMe\n        adoptedDomain.roarr = {\n          context: {\n            ...getParentDomainContext(),\n            ...context,\n          },\n        };\n\n        return routine();\n      });\n  };\n\n  for (const logLevel of Object.keys(logLevels)) {\n    // eslint-disable-next-line id-length, unicorn/prevent-abbreviations\n    log[logLevel] = (a, b, c, d, e, f, g, h, i, k) => {\n      return log.child({\n        logLevel: logLevels[logLevel],\n      })(a, b, c, d, e, f, g, h, i, k);\n    };\n  }\n\n  // @see https://github.com/facebook/flow/issues/6705\n  // $FlowFixMe\n  return log;\n};\n\nexport default createLogger;\n"], "file": "createLogger.js"}