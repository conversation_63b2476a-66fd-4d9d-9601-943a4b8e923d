{"version": 3, "sources": ["../../src/utilities/bindHttpMethod.js"], "names": ["originalMethod", "agent", "forceGlobalAgent", "args", "url", "options", "callback", "URL", "http", "globalAgent", "https"], "mappings": ";;;;;;;AAEA;;AACA;;;;AAIA;wBACgBA,c,EAA0BC,K,EAAkBC,gB,KAA8B;AACxF;AACA,SAAO,CAAC,GAAGC,IAAJ,KAAgB;AACrB,QAAIC,GAAJ;AACA,QAAIC,OAAJ;AACA,QAAIC,QAAJ;;AAEA,QAAI,OAAOH,IAAI,CAAC,CAAD,CAAX,KAAmB,QAAnB,IAA+BA,IAAI,CAAC,CAAD,CAAJ,YAAmBI,GAAtD,EAA2D;AACzDH,MAAAA,GAAG,GAAGD,IAAI,CAAC,CAAD,CAAV;;AAEA,UAAI,OAAOA,IAAI,CAAC,CAAD,CAAX,KAAmB,UAAvB,EAAmC;AACjCE,QAAAA,OAAO,GAAG,EAAV;AACAC,QAAAA,QAAQ,GAAGH,IAAI,CAAC,CAAD,CAAf;AACD,OAHD,MAGO;AACLE,QAAAA,OAAO,GAAG,EACR,GAAGF,IAAI,CAAC,CAAD;AADC,SAAV;AAGAG,QAAAA,QAAQ,GAAGH,IAAI,CAAC,CAAD,CAAf;AACD;AACF,KAZD,MAYO;AACLE,MAAAA,OAAO,GAAG,EACR,GAAGF,IAAI,CAAC,CAAD;AADC,OAAV;AAGAG,MAAAA,QAAQ,GAAGH,IAAI,CAAC,CAAD,CAAf;AACD;;AAED,QAAID,gBAAJ,EAAsB;AACpBG,MAAAA,OAAO,CAACJ,KAAR,GAAgBA,KAAhB;AACD,KAFD,MAEO;AACL,UAAI,CAACI,OAAO,CAACJ,KAAb,EAAoB;AAClBI,QAAAA,OAAO,CAACJ,KAAR,GAAgBA,KAAhB;AACD;;AAED,UAAII,OAAO,CAACJ,KAAR,KAAkBO,cAAKC,WAAvB,IAAsCJ,OAAO,CAACJ,KAAR,KAAkBS,eAAMD,WAAlE,EAA+E;AAC7EJ,QAAAA,OAAO,CAACJ,KAAR,GAAgBA,KAAhB;AACD;AACF;;AAED,QAAIG,GAAJ,EAAS;AACP;AACA,aAAOJ,cAAc,CAACI,GAAD,EAAMC,OAAN,EAAeC,QAAf,CAArB;AACD,KAHD,MAGO;AACL,aAAON,cAAc,CAACK,OAAD,EAAUC,QAAV,CAArB;AACD;AACF,GA1CD;AA2CD,C", "sourcesContent": ["// @flow\n\nimport http from 'http';\nimport https from 'https';\n\ntype AgentType = http.Agent | https.Agent;\n\n// eslint-disable-next-line flowtype/no-weak-types\nexport default (originalMethod: Function, agent: AgentType, forceGlobalAgent: boolean) => {\n  // eslint-disable-next-line unicorn/prevent-abbreviations\n  return (...args: *) => {\n    let url;\n    let options;\n    let callback;\n\n    if (typeof args[0] === 'string' || args[0] instanceof URL) {\n      url = args[0];\n\n      if (typeof args[1] === 'function') {\n        options = {};\n        callback = args[1];\n      } else {\n        options = {\n          ...args[1],\n        };\n        callback = args[2];\n      }\n    } else {\n      options = {\n        ...args[0],\n      };\n      callback = args[1];\n    }\n\n    if (forceGlobalAgent) {\n      options.agent = agent;\n    } else {\n      if (!options.agent) {\n        options.agent = agent;\n      }\n\n      if (options.agent === http.globalAgent || options.agent === https.globalAgent) {\n        options.agent = agent;\n      }\n    }\n\n    if (url) {\n      // $FlowFixMe\n      return originalMethod(url, options, callback);\n    } else {\n      return originalMethod(options, callback);\n    }\n  };\n};\n"], "file": "bindHttpMethod.js"}