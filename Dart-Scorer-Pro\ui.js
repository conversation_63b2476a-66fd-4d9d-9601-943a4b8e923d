// ui.js

// --- Screen Management ---
const gameSetupScreen = document.getElementById('game-setup-screen');
const gamePlayScreen = document.getElementById('game-play-screen');
const winnerModal = document.getElementById('winner-modal');
const winnerModalContent = document.getElementById('winner-modal-content');

export function displayGameSetupScreen() {
    gameSetupScreen.classList.remove('hidden');
    gamePlayScreen.classList.add('hidden');
    hideWinnerModal();
}

export function displayGamePlayScreen(startingScore) {
    gameSetupScreen.classList.add('hidden');
    gamePlayScreen.classList.remove('hidden');
    hideWinnerModal();
    document.getElementById('game-title').textContent = `${startingScore} Game`;
}

// --- Player List (Setup Screen) ---
const playerListDiv = document.getElementById('player-list');
export function renderPlayerList(players, onRemoveCallback) {
    playerListDiv.innerHTML = '';
    if (players.length === 0) {
        playerListDiv.innerHTML = '<p class="text-gray-400 italic">No players added yet.</p>';
        return;
    }
    players.forEach(player => {
        const playerDiv = document.createElement('div');
        playerDiv.className = 'flex justify-between items-center bg-gray-700 p-2 rounded-md';
        playerDiv.innerHTML = `
            <span class="text-gray-200">${player.name}</span>
            <button data-player-name="${player.name}" class="remove-player-btn text-red-400 hover:text-red-300 text-xs font-semibold py-1 px-2 rounded-md">Remove</button>
        `;
        playerDiv.querySelector('.remove-player-btn').addEventListener('click', () => onRemoveCallback(player.name));
        playerListDiv.appendChild(playerDiv);
    });
}

// --- Scoreboard (Game Play Screen) ---
const scoreboardDiv = document.getElementById('scoreboard');
export function updateScoreboard(players, currentPlayerIndex) {
    scoreboardDiv.innerHTML = '';
    players.forEach((player, index) => {
        const card = document.createElement('div');
        card.className = `player-card bg-gray-800 p-4 rounded-lg shadow-md border-2 border-transparent transition-all duration-300 ${index === currentPlayerIndex ? 'active border-yellow-400 scale-105' : 'border-gray-700'}`;
        card.innerHTML = `
            <h3 class="text-xl font-semibold truncate ${index === currentPlayerIndex ? 'text-yellow-400' : 'text-sky-400'} mb-1">${player.name}</h3>
            <p class="text-5xl font-bold ${player.currentScore <= 0 ? 'text-green-400' : 'text-gray-100'} mb-2">${player.currentScore}</p>
            <div class="text-xs text-gray-400">
                <span>Throws: ${player.stats.totalThrows}</span> | 
                <span>Avg: ${player.stats.averageScorePerTurn > 0 ? player.stats.averageScorePerTurn.toFixed(1) : '0.0'}</span>
            </div>
            <div class="text-xs text-gray-400">
                <span>Last Turn: ${player.throwsHistory.length > 0 ? (player.throwsHistory[player.throwsHistory.length-1].isBust ? 'BUST' : player.throwsHistory[player.throwsHistory.length-1].turnScore) : '-'}</span>
            </div>
        `;
        scoreboardDiv.appendChild(card);
    });
}
export function clearScoreboard() {
    scoreboardDiv.innerHTML = '';
}

// --- Current Player and Turn Display ---
const currentPlayerIndicator = document.getElementById('current-player-turn-indicator');
const currentTurnDartsDisplay = document.getElementById('current-turn-darts-display');
const currentTurnTotalScoreDisplay = document.getElementById('current-turn-total-score');
const playerCheckoutMessage = document.getElementById('player-checkout-message');


export function updateCurrentPlayerIndicator(name) {
    currentPlayerIndicator.textContent = `${name}'s Turn`;
}

export function updateCurrentTurnDarts(dartsThisTurn, getDartValueFn) {
    currentTurnDartsDisplay.innerHTML = '';
    let total = 0;
    for (let i = 0; i < 3; i++) {
        const dartSpan = document.createElement('span');
        dartSpan.className = 'w-1/3 text-center';
        if (dartsThisTurn[i]) {
            dartSpan.textContent = dartsThisTurn[i].text || `${dartsThisTurn[i].multiplier}x${dartsThisTurn[i].score}`;
            total += dartsThisTurn[i].value;
        } else {
            dartSpan.textContent = '-';
        }
        currentTurnDartsDisplay.appendChild(dartSpan);
    }
    currentTurnTotalScoreDisplay.textContent = total;
}
export function clearCurrentTurnDarts() {
    updateCurrentTurnDarts([], () => 0);
}

export function updatePlayerCheckoutMessage(player, mustFinishOnDouble, getCheckoutSuggestionsFn) {
    if (!player) {
        playerCheckoutMessage.textContent = '';
        return;
    }
    const score = player.currentScore;
    if (score <= 170 && score > 1 && (mustFinishOnDouble ? score % 2 === 0 || score === 50 || score <= 40 : true)) {
        const suggestions = getCheckoutSuggestionsFn(score, mustFinishOnDouble);
        if (suggestions.length > 0) {
            playerCheckoutMessage.textContent = `Possible checkout: ${suggestions[0]}`; // Show first suggestion
        } else {
             playerCheckoutMessage.textContent = (score <= 1 || (mustFinishOnDouble && score === 1)) ? "No checkout." : `Remaining: ${score}`;
        }
    } else if (score === 0) {
         playerCheckoutMessage.textContent = 'Game Shot!';
    }
    else {
        playerCheckoutMessage.textContent = `Remaining: ${score}`;
    }
}


// --- Game Log ---
const gameLogDiv = document.getElementById('game-log');
export function updateGameLog(logEntries) {
    gameLogDiv.innerHTML = logEntries.map(entry => `<p>${entry}</p>`).join('');
    gameLogDiv.scrollTop = gameLogDiv.scrollHeight; // Auto-scroll to bottom
}
export function clearGameLog() {
    gameLogDiv.innerHTML = '';
}

// --- Round Display ---
const currentRoundDisplay = document.getElementById('current-round');
export function updateRoundDisplay(round) {
    currentRoundDisplay.textContent = round;
}

// --- Checkout Helper ---
const checkoutSuggestionsDiv = document.getElementById('checkout-suggestions');
export function updateCheckoutSuggestions(suggestions) {
    if (suggestions && suggestions.length > 0) {
        checkoutSuggestionsDiv.innerHTML = suggestions.map(s => `<p class="p-1 hover:bg-gray-700 rounded">${s}</p>`).join('');
    } else {
        checkoutSuggestionsDiv.innerHTML = '<p class="text-gray-500 italic">No common checkouts or score too high.</p>';
    }
}
export function clearCheckoutSuggestions(){
    checkoutSuggestionsDiv.innerHTML = '<p class="text-gray-500 italic">...</p>';
}

// --- Winner Modal ---
export function showWinnerModal(winner, gameType, history, stats) {
    document.getElementById('winner-name').textContent = winner.name;
    document.getElementById('winner-details').textContent = `won the ${gameType} game!`;
    
    const statsDiv = document.getElementById('winner-stats');
    statsDiv.innerHTML = `
        <p><strong>Total Darts Thrown:</strong> ${stats.totalThrows}</p>
        <p><strong>Total Score:</strong> ${stats.totalScoreThrown}</p>
        <p><strong>Average per Turn (scored):</strong> ${stats.averageScorePerTurn > 0 ? stats.averageScorePerTurn.toFixed(2) : 'N/A'}</p>
        <p><strong>Highest Turn:</strong> ${stats.highestTurnScore}</p>
        <p><strong>Doubles Hit:</strong> ${stats.doublesHit}</p>
        <p><strong>Triples Hit:</strong> ${stats.triplesHit}</p>
        <p><strong>180s:</strong> ${stats.oneEighties}</p>
    `;

    winnerModal.classList.remove('hidden');
    setTimeout(() => { // For transition
        winnerModalContent.classList.remove('scale-95', 'opacity-0');
        winnerModalContent.classList.add('scale-100', 'opacity-100');
    }, 10);
}

export function hideWinnerModal() {
    winnerModalContent.classList.add('scale-95', 'opacity-0');
    winnerModalContent.classList.remove('scale-100', 'opacity-100');
    setTimeout(() => {
       winnerModal.classList.add('hidden');
    }, 300); // Match transition duration
}

// --- Toast Notifications ---
const toastContainer = document.getElementById('toast-container');
export function showToast(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = 'toast px-4 py-3 rounded-md shadow-lg text-sm font-medium';
    
    let bgColor = 'bg-gray-700 text-white'; // Default / Info
    if (type === 'success') bgColor = 'bg-green-500 text-white';
    else if (type === 'error') bgColor = 'bg-red-500 text-white';
    else if (type === 'warn') bgColor = 'bg-yellow-500 text-gray-900';
    
    toast.classList.add(...bgColor.split(' '));
    toast.textContent = message;

    toastContainer.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateY(0)';
    }, 10);

    // Animate out and remove
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateY(20px)';
        setTimeout(() => {
            toast.remove();
        }, 500); // Corresponds to animation duration
    }, duration);
}

// --- Saved Games List ---
const savedGamesListDiv = document.getElementById('saved-games-list');
export function updateSavedGamesList(gameIds, loadCallback) {
    savedGamesListDiv.innerHTML = '';
    if (!gameIds || gameIds.length === 0) {
        savedGamesListDiv.innerHTML = '<p class="italic text-gray-500">No saved games found.</p>';
        return;
    }
    const list = document.createElement('ul');
    list.className = 'space-y-1 max-h-24 overflow-y-auto';
    gameIds.forEach(id => {
        const listItem = document.createElement('li');
        listItem.className = 'text-sky-400 hover:text-sky-300 cursor-pointer underline';
        listItem.textContent = id;
        listItem.addEventListener('click', () => loadCallback(id));
        list.appendChild(listItem);
    });
    savedGamesListDiv.appendChild(list);
}

// --- User ID Display ---
const userIdValueSpan = document.getElementById('user-id-value');
export function updateUserIdDisplay(userId) {
    if (userIdValueSpan) {
        userIdValueSpan.textContent = userId || 'N/A';

        // Add storage type indicator with color coding
        if (userId && userId.includes('Offline')) {
            userIdValueSpan.style.color = '#f59e0b'; // Orange for offline
            userIdValueSpan.title = 'Using offline storage - games saved locally on this device';
        } else if (userId && userId !== 'N/A' && !userId.includes('Error')) {
            userIdValueSpan.style.color = '#10b981'; // Green for online
            userIdValueSpan.title = 'Using online storage - games saved to cloud';
        } else {
            userIdValueSpan.style.color = '#ef4444'; // Red for no storage
            userIdValueSpan.title = 'No storage available - cannot save/load games';
        }
    }
}

