// main.js
// Modules to control application life and create native browser window
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

function createWindow() {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 900,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true, // Recommended for security
      nodeIntegration: false, // Recommended for security
      // sandbox: true // Consider for enhanced security if not using nodeIntegrationInWorker
    },
    icon: path.join(__dirname, 'assets', 'icon.png') // Optional: if you have an icon
  });

  // Load the index.html of the app.
  mainWindow.loadFile('index.html');

  // Open the DevTools.
  // mainWindow.webContents.openDevTools(); // Uncomment to show DevTools on start

  // Handle window resize if needed for responsive design
  mainWindow.on('resize', () => {
    // Example: send new dimensions to renderer if needed
    // mainWindow.webContents.send('window-resized', mainWindow.getBounds());
  });
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  createWindow();

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.

// Example IPC handler (if needed)
ipcMain.handle('get-app-path', () => {
  return app.getAppPath();
});

// You might want to add auto-updater logic here for a production app
// const { autoUpdater } = require('electron-updater');
// autoUpdater.checkForUpdatesAndNotify();
