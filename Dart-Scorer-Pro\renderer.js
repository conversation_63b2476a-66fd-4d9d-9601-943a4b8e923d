// renderer.js - Offline-Only Version
// Firebase imports removed for Windows-only optimized build

import * as Player from './player.js';
import * as Game from './game.js';
import * as UI from './ui.js';
import * as Dartboard from './dartboard.js';
import * as OfflineStorage from './offline-storage.js';
import * as Checkout from './checkout.js';

// --- Application State ---
let players = [];
let currentGame = null;
let currentUserId = null;
let isAuthReady = true; // Always ready in offline mode
let useOfflineStorage = true; // Always use offline storage in optimized build

// --- DOM Elements ---
// Game Setup Screen
const gameSetupScreen = document.getElementById('game-setup-screen');
const newPlayerNameInput = document.getElementById('new-player-name');
const addPlayerBtn = document.getElementById('add-player-btn');
const gameModeSelect = document.getElementById('game-mode-select');
const customStartScoreInput = document.getElementById('custom-start-score');
const finishOnDoubleCheckbox = document.getElementById('finish-on-double-checkbox');
const startGameBtn = document.getElementById('start-game-btn');
const loadGameIdInput = document.getElementById('load-game-id-input');
const loadGameBtn = document.getElementById('load-game-btn');
const exportGamesBtn = document.getElementById('export-games-btn');
const importGamesBtn = document.getElementById('import-games-btn');
const importFileInput = document.getElementById('import-file-input');
const storageInfoDiv = document.getElementById('storage-info');

// Game Play Screen
const gamePlayScreen = document.getElementById('game-play-screen');
const quitGameBtn = document.getElementById('quit-game-btn');
const saveGameBtn = document.getElementById('save-game-btn');
const nextPlayerBtn = document.getElementById('next-player-btn');
const bustBtn = document.getElementById('bust-btn');
const undoLastDartBtn = document.getElementById('undo-last-dart-btn');
const undoLastTurnBtn = document.getElementById('undo-last-turn-btn');
const manualDartInput = document.getElementById('manual-dart-input');
const submitManualDartBtn = document.getElementById('submit-manual-dart-btn');

// Winner Modal
const winnerModal = document.getElementById('winner-modal');
const playAgainBtn = document.getElementById('play-again-btn');
const newGameSetupBtn = document.getElementById('new-game-setup-btn');

// --- Offline Storage Initialization ---
async function initializeStorage() {
    console.log("Initializing offline-only storage...");

    // Check if offline storage is available
    if (OfflineStorage.isOfflineStorageAvailable()) {
        const storageInfo = OfflineStorage.getStorageInfo();
        currentUserId = storageInfo.userId;
        UI.updateUserIdDisplay(`Offline - ${storageInfo.userId.substring(0, 8)}...`);
        UI.showToast("Dart Scorer Pro - Offline Mode", "success");

        // Load saved games list for offline mode
        await OfflineStorage.listSavedGames((gameIds) => UI.updateSavedGamesList(gameIds, loadGameFromList));
    } else {
        UI.showToast("Storage not available", "error");
        UI.updateUserIdDisplay("N/A - Storage Error");
    }

    checkAuthAndProceed();
}

function checkAuthAndProceed() {
    if (!isAuthReady) return; // Wait until storage initialization is complete

    // Enable features for offline storage
    if (currentUserId && useOfflineStorage) {
        saveGameBtn.disabled = false;
        loadGameBtn.disabled = false;
        exportGamesBtn.disabled = false;
        importGamesBtn.disabled = false;
        updateStorageInfo();
    } else {
        saveGameBtn.disabled = true;
        loadGameBtn.disabled = true;
        exportGamesBtn.disabled = true;
        importGamesBtn.disabled = true;
        UI.showToast("Storage system not available. Save/Load disabled.", "warn");
    }
}


// --- Event Listeners ---
function setupEventListeners() {
    addPlayerBtn.addEventListener('click', handleAddPlayer);
    newPlayerNameInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') handleAddPlayer();
    });

    gameModeSelect.addEventListener('change', () => {
        customStartScoreInput.classList.toggle('hidden', gameModeSelect.value !== 'custom');
    });

    startGameBtn.addEventListener('click', handleStartGame);
    quitGameBtn.addEventListener('click', handleQuitGame);
    saveGameBtn.addEventListener('click', handleSaveGame);
    loadGameBtn.addEventListener('click', handleLoadGame);

    // Storage management event listeners
    exportGamesBtn.addEventListener('click', handleExportGames);
    importGamesBtn.addEventListener('click', () => importFileInput.click());
    importFileInput.addEventListener('change', handleImportGames);


    nextPlayerBtn.addEventListener('click', handleNextPlayer);
    bustBtn.addEventListener('click', handleBust);
    undoLastDartBtn.addEventListener('click', handleUndoLastDart);
    undoLastTurnBtn.addEventListener('click', handleUndoLastTurn);

    submitManualDartBtn.addEventListener('click', handleManualDartInput);
    manualDartInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') handleManualDartInput();
    });

    // Initialize dartboard (will be re-initialized when game starts)
    initializeDartboardForGame();

    playAgainBtn.addEventListener('click', handlePlayAgain);
    newGameSetupBtn.addEventListener('click', () => {
        UI.hideWinnerModal();
        resetToSetupScreen();
    });
}

// --- Helper Functions ---
function initializeDartboardForGame() {
    console.log("Initializing dartboard for game...");
    const dartboardElement = document.getElementById('interactive-dartboard');
    if (!dartboardElement) {
        console.error("Dartboard SVG element not found during initialization!");
        return;
    }
    console.log("Dartboard element found, proceeding with initialization");

    Dartboard.initDartboard((dart) => {
        console.log("Dartboard clicked:", dart); // Debug log
        if (!currentGame || Game.getCurrentPlayer(currentGame).dartsThisTurn.length >= 3) {
            console.log("Cannot record dart - no game or 3 darts already thrown");
            return;
        }
        if (dart && typeof dart.score !== 'undefined' && typeof dart.multiplier !== 'undefined') {
            Game.recordDart(currentGame, dart.score, dart.multiplier, dart.text);
            updateGameUI();
            UI.showToast(`Dart recorded: ${dart.text} (${dart.value} points)`, "success");
        } else {
            console.error("Invalid dart object:", dart);
            UI.showToast(`Invalid dart clicked`, "error");
        }
    });

    // Ensure pointer events are enabled
    dartboardElement.style.pointerEvents = 'auto';
    console.log("Dartboard initialization complete, pointer events enabled");
}

// --- Handler Functions ---
function handleAddPlayer() {
    const name = newPlayerNameInput.value.trim();
    if (name) {
        if (players.length < 10) { // Max 10 players for sanity
            const newPlayer = Player.createPlayer(name);
            players.push(newPlayer);
            UI.renderPlayerList(players, (playerName) => { // Pass remove callback
                players = Player.removePlayer(players, playerName);
                UI.renderPlayerList(players, () => {}); // Re-render
            });
            newPlayerNameInput.value = '';
            newPlayerNameInput.focus();
        } else {
            UI.showToast("Maximum 10 players allowed.", "warn");
        }
    } else {
        UI.showToast("Player name cannot be empty.", "warn");
    }
}

function handleStartGame() {
    if (players.length === 0) {
        UI.showToast("Please add at least one player.", "error");
        return;
    }
    let startScore = parseInt(gameModeSelect.value);
    if (gameModeSelect.value === 'custom') {
        startScore = parseInt(customStartScoreInput.value);
        if (isNaN(startScore) || startScore <= 1) {
            UI.showToast("Custom start score must be a number greater than 1.", "error");
            return;
        }
    }
    const mustFinishOnDouble = finishOnDoubleCheckbox.checked;

    // Reset player scores and history for a new game
    players.forEach(p => {
        p.currentScore = startScore;
        p.throwsHistory = [];
        p.stats = Player.resetPlayerStats();
    });
    
    currentGame = Game.createGame(players, startScore, mustFinishOnDouble);
    UI.displayGamePlayScreen(currentGame.settings.startingScore);

    // Re-initialize dartboard for the new game
    initializeDartboardForGame();

    UI.updateScoreboard(currentGame.players, currentGame.currentPlayerIndex);
    UI.updateGameLog(currentGame.gameLog);
    UI.updateRoundDisplay(currentGame.currentRound);
    updatePlayerTurnUI();
    updateGameButtonStates();
     UI.showToast(`Game started: ${startScore} points!`, "success");
}

function handleQuitGame() {
    // Could add a confirmation modal here
    resetToSetupScreen();
}

function resetToSetupScreen() {
    currentGame = null;
    players = []; // Or persist players if desired between games; for now, clear them
    UI.renderPlayerList(players, () => {});
    newPlayerNameInput.value = '';
    gameModeSelect.value = '501';
    customStartScoreInput.value = '';
    customStartScoreInput.classList.add('hidden');
    finishOnDoubleCheckbox.checked = true;
    UI.displayGameSetupScreen();
    UI.clearGameLog();
    UI.clearScoreboard();
    UI.clearCurrentTurnDarts();
    UI.clearCheckoutSuggestions();
    if (isAuthReady && currentUserId) {
        OfflineStorage.listSavedGames( (gameIds) => UI.updateSavedGamesList(gameIds, loadGameFromList));
    }
}

function handleManualDartInput() {
    if (!currentGame || Game.getCurrentPlayer(currentGame).dartsThisTurn.length >= 3) return;
    const inputText = manualDartInput.value.trim();
    const dart = Dartboard.parseDart(inputText);
    if (dart) {
        Game.recordDart(currentGame, dart.score, dart.multiplier, dart.text);
        updateGameUI();
        manualDartInput.value = '';
        manualDartInput.focus();
    } else {
        UI.showToast(`Invalid input: "${inputText}". Try "T20", "D16", "S5", "Bull", "DB25".`, "error");
        manualDartInput.select();
    }
}

function handleNextPlayer() {
    if (!currentGame) return;
    const player = Game.getCurrentPlayer(currentGame);
    if (player.dartsThisTurn.length === 0 && player.currentScore > 0) { // Allow proceeding if player has no throws (e.g. strategic pass or accidental click)
        UI.showToast(`${player.name} has not thrown any darts this turn.`, "warn");
        // Or force at least one dart / or confirm
    }
    
    const turnOutcome = Game.endTurn(currentGame); // This will handle score calculation, bust check, and win check.
    
    updateGameUI();

    if (turnOutcome.isWin) {
        UI.showWinnerModal(turnOutcome.winner, currentGame.settings.startingScore, turnOutcome.winner.throwsHistory, turnOutcome.winner.stats);
        // Game ends here, further actions via modal buttons.
    } else {
         // If not a win, move to the next player or next round
        Game.moveToNextPlayer(currentGame);
        updateGameUI(); // Update again for next player's turn
        UI.showToast(`${Game.getCurrentPlayer(currentGame).name}'s turn.`, "info");
    }
}

function handleBust() {
    if (!currentGame) return;
    Game.recordBust(currentGame);
    Game.moveToNextPlayer(currentGame);
    updateGameUI();
    UI.showToast(`Bust! ${Game.getCurrentPlayer(currentGame).name}'s turn.`, "info");
}

function handleUndoLastDart() {
    if (!currentGame) return;
    Game.undoLastDart(currentGame);
    updateGameUI();
}

function handleUndoLastTurn() {
    if (!currentGame) return;
    Game.undoLastTurn(currentGame);
    updateGameUI();
    UI.showToast("Last turn undone.", "info");
}

function updateGameUI() {
    if (!currentGame) return;
    UI.updateScoreboard(currentGame.players, currentGame.currentPlayerIndex);
    UI.updateGameLog(currentGame.gameLog);
    UI.updateRoundDisplay(currentGame.currentRound);
    updatePlayerTurnUI();
    updateGameButtonStates();
}

function updatePlayerTurnUI() {
    const player = Game.getCurrentPlayer(currentGame);
    UI.updateCurrentPlayerIndicator(player.name);
    UI.updateCurrentTurnDarts(player.dartsThisTurn, Dartboard.getDartValue);
    
    const potentialScore = player.currentScore - Game.calculateTurnScore(player.dartsThisTurn, Dartboard.getDartValue);
    UI.updateCheckoutSuggestions(Checkout.getCheckoutSuggestions(potentialScore, currentGame.settings.mustFinishOnDouble));
    UI.updatePlayerCheckoutMessage(player, currentGame.settings.mustFinishOnDouble, Checkout.getCheckoutSuggestions);

}

function updateGameButtonStates() {
    if (!currentGame) return;
    const player = Game.getCurrentPlayer(currentGame);
    const dartsThrownThisTurn = player.dartsThisTurn.length;

    nextPlayerBtn.disabled = dartsThrownThisTurn > 3; // Should not happen if input is limited
    bustBtn.disabled = dartsThrownThisTurn === 0; // Can bust after any dart
    undoLastDartBtn.disabled = dartsThrownThisTurn === 0;
    undoLastTurnBtn.disabled = currentGame.gameLog.length === 0 && currentGame.currentRound === 1 && currentGame.players.every(p => p.throwsHistory.length === 0);

    // Disable dart input if 3 darts are thrown
    manualDartInput.disabled = dartsThrownThisTurn >= 3;
    submitManualDartBtn.disabled = dartsThrownThisTurn >= 3;
    // Consider disabling dartboard clicks too
    const dartboardElement = document.getElementById('interactive-dartboard');
    if (dartboardElement) {
        dartboardElement.style.pointerEvents = dartsThrownThisTurn >= 3 ? 'none' : 'auto';
        console.log(`Dartboard pointer events set to: ${dartboardElement.style.pointerEvents} (darts thrown: ${dartsThrownThisTurn})`);
    } else {
        console.error("Dartboard element not found when updating button states");
    }
}

async function handleSaveGame() {
    if (!currentGame || !isAuthReady) {
        UI.showToast("Cannot save game. Not initialized or no game active.", "error");
        return;
    }

    if (!currentUserId && !useOfflineStorage) {
        UI.showToast("No storage system available.", "error");
        return;
    }

    try {
        let gameId;

        gameId = await OfflineStorage.saveGame(currentGame);
        UI.showToast(`Game saved offline with ID: ${gameId.substring(0, 8)}...`, "success");
        // Update the list of saved games in the UI
        await OfflineStorage.listSavedGames((gameIds) => UI.updateSavedGamesList(gameIds, loadGameFromList));

    } catch (error) {
        console.error("Error saving game:", error);
        UI.showToast(`Error saving game: ${error.message}`, "error");
    }
}

async function handleLoadGame() {
    if (!isAuthReady) {
        UI.showToast("Cannot load game. Storage not ready.", "error");
        return;
    }

    if (!currentUserId && !useOfflineStorage) {
        UI.showToast("No storage system available.", "error");
        return;
    }

    const gameIdToLoad = loadGameIdInput.value.trim();
    if (!gameIdToLoad) {
        UI.showToast("Please enter a Game ID to load.", "warn");
        return;
    }
    loadGameFromList(gameIdToLoad);
}

async function loadGameFromList(gameId) {
    if (!gameId || !isAuthReady) {
        UI.showToast("Cannot load game. Not initialized.", "error");
        return;
    }

    if (!currentUserId && !useOfflineStorage) {
        UI.showToast("No storage system available.", "error");
        return;
    }

    try {
        let loadedGameData;

        loadedGameData = await OfflineStorage.loadGame(gameId);

        if (loadedGameData) {
            // Reconstruct player objects if necessary (e.g., if methods are not serialized)
            // For now, assume structure is fine or handle in Storage.loadGame
            players = loadedGameData.players.map(pData => Player.rehydratePlayer(pData));

            currentGame = Game.rehydrateGame(loadedGameData, players);

            UI.displayGamePlayScreen(currentGame.settings.startingScore);
            // Re-initialize dartboard for the loaded game
            initializeDartboardForGame();
            updateGameUI();
            loadGameIdInput.value = '';

            UI.showToast(`Game ${gameId.substring(0, 8)}... loaded from offline storage!`, "success");
        } else {
            UI.showToast(`Game with ID ${gameId} not found.`, "error");
        }
    } catch (error) {
        console.error("Error loading game:", error);
        UI.showToast(`Error loading game: ${error.message}`, "error");
    }
}


function handlePlayAgain() {
    UI.hideWinnerModal();
    if (currentGame) {
        const oldSettings = currentGame.settings;
        const oldPlayers = currentGame.players.map(p => Player.createPlayer(p.name)); // Keep names, reset scores

        players = oldPlayers; // Use the re-created players for the new game
        players.forEach(p => {
            p.currentScore = oldSettings.startingScore;
            p.throwsHistory = [];
            p.stats = Player.resetPlayerStats();
        });

        currentGame = Game.createGame(players, oldSettings.startingScore, oldSettings.mustFinishOnDouble);
        UI.displayGamePlayScreen(currentGame.settings.startingScore);
        // Re-initialize dartboard for the new game
        initializeDartboardForGame();
        updateGameUI();
        UI.showToast("New game started with same players and settings!", "success");
    } else {
        resetToSetupScreen(); // Fallback
    }
}

// --- Storage Management Functions ---
async function handleExportGames() {
    try {
        let exportData;

        if (useOfflineStorage) {
            exportData = await OfflineStorage.exportGames();
        } else {
            UI.showToast("Export only available in offline mode", "warn");
            return;
        }

        // Create and download the file
        const blob = new Blob([exportData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `dart-scorer-games-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        UI.showToast("Games exported successfully!", "success");

    } catch (error) {
        console.error("Error exporting games:", error);
        UI.showToast(`Error exporting games: ${error.message}`, "error");
    }
}

async function handleImportGames(event) {
    const file = event.target.files[0];
    if (!file) return;

    try {
        const text = await file.text();
        let importedCount;

        if (useOfflineStorage) {
            importedCount = await OfflineStorage.importGames(text);
        } else {
            UI.showToast("Import only available in offline mode", "warn");
            return;
        }

        UI.showToast(`Successfully imported ${importedCount} games!`, "success");

        // Refresh the saved games list
        if (useOfflineStorage) {
            await OfflineStorage.listSavedGames((gameIds) => UI.updateSavedGamesList(gameIds, loadGameFromList));
        }

        // Update storage info
        updateStorageInfo();

    } catch (error) {
        console.error("Error importing games:", error);
        UI.showToast(`Error importing games: ${error.message}`, "error");
    }

    // Clear the file input
    event.target.value = '';
}

function updateStorageInfo() {
    if (!storageInfoDiv) return;

    if (useOfflineStorage) {
        const info = OfflineStorage.getStorageInfo();
        storageInfoDiv.innerHTML = `
            <div>Games saved: ${info.gameCount}/${info.maxGames}</div>
            <div>Storage used: ${info.dataSizeKB} KB</div>
            <div>User ID: ${info.userId.substring(0, 12)}...</div>
        `;
    } else {
        storageInfoDiv.innerHTML = `<div>Online storage - info not available</div>`;
    }
}


// --- Initialization ---
document.addEventListener('DOMContentLoaded', async () => {
    await initializeStorage(); // Initialize offline storage
    setupEventListeners();
    UI.displayGameSetupScreen(); // Start with setup screen
    checkAuthAndProceed();
});

