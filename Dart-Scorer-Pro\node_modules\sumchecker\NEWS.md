# Changes by Version

## Unreleased

## [3.0.1] - 2019-11-25

### Fixed

* Correctly export symbols in TypeScript definition (#26)

## [3.0.0] - 2019-04-30

### Added

* TypeScript typings file (#12)

### Removed

* Support for Node &lt; 8 (#10)

## [2.0.2] - 2017-03-11

### Added

* Node 8 support (#6)

## [2.0.1] - 2017-01-26

### Fixed

* Include `index.js` in the NPM package again

## [2.0.0] - 2017-01-26

### Removed

* Support for Node 0.10 and 0.12 (#4)

## [1.3.1] - 2017-03-11

### Added

* Node 8 support (#6)

## [1.3.0] - 2016-12-05

### Added

* Human-readable messages for Errors (#2)

## [1.2.0] - 2016-09-15

### Added

* Support for Node 0.10 and 0.12 via Babel (#1)

## [1.1.0] - 2016-09-06

### Added

* Optional `defaultTextEncoding` parameter in the `ChecksumValidator` constructor
* Access to the underlying `ChecksumValidator` class

## [1.0.0] - 2016-09-05

Initial release.

[3.0.1]: https://github.com/malept/sumchecker/compare/v3.0.0...v3.0.1
[3.0.0]: https://github.com/malept/sumchecker/compare/v2.0.2...v3.0.0
[2.0.2]: https://github.com/malept/sumchecker/compare/v2.0.1...v2.0.2
[2.0.1]: https://github.com/malept/sumchecker/compare/v2.0.0...v2.0.1
[2.0.0]: https://github.com/malept/sumchecker/compare/v1.3.0...v2.0.0
[1.3.1]: https://github.com/malept/sumchecker/compare/v1.3.0...v1.3.1
[1.3.0]: https://github.com/malept/sumchecker/compare/v1.2.0...v1.3.0
[1.2.0]: https://github.com/malept/sumchecker/compare/v1.1.0...v1.2.0
[1.1.0]: https://github.com/malept/sumchecker/compare/v1.0.0...v1.1.0
[1.0.0]: https://github.com/malept/sumchecker/releases/tag/v1.0.0
